import React, { useEffect, useState } from 'react';
import { AlertCircle, ExternalLink, Upload, Receipt, Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { WorkOrderItem } from "@/components/workOrders/WorkOrderItem";
import { getFromApi, uploadGivenFile, postToApi } from '@/lib/api';
import { currencyValue } from "@/lib/utils";

export function WorkOrdersTab({ user, business, location, provider }) {
  const { toast } = useToast();
  const [workOrders, setWorkOrders] = useState([]);
  const [selectedWorkOrder, setSelectedWorkOrder] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [errorData, setErrorData] = useState(null);
  const [isCreatingBatch, setIsCreatingBatch] = useState(false);
  const [batchResultData, setBatchResultData] = useState(null);

  // Create a ref for the file input
  const fileInputRef = React.useRef(null);

  useEffect(() => {
    if (provider) {
      fetchWorkOrders();
    }
  }, [provider, user]);

  const fetchWorkOrders = () => {
    if (provider) {
      let url = `/api/v1/provider/${provider.id}/work_orders/${business.id}/90`;
      getFromApi(url, user, (response) => {
        console.log("WorkOrdersPage useEffect", response.result)
        let workorders = response.result || [];
        setWorkOrders(workorders.sort((a, b) => a.id - b.id));
      }, (error) => {
        console.error("Error fetching work orders:", error);
      });
    }
  };

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Only accept Excel files
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast({
        title: "Invalid File",
        description: "Please upload an Excel file (.xlsx or .xls)",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    if (provider && user) {
      const path = `/api/v1/provider/${provider.id}/import/${business.id}/clickup`;

      uploadGivenFile("POST", path, user, {}, 'work_orders', file,
        () => { /* onProgress */ },
        () => { /* onLoad */ },
        (response) => {
          console.log("response:", response)
          if (response.result) {
            let addedCount = response.result.addedCount
            let errorCount = response.result.errorCount

            // If there are errors, show the error dialog
            if (errorCount > 0) {
              setErrorData({
                addedCount: response.result.addedCount,
                errorCount: response.result.errorCount,
                url: response.result.url
              });
              setIsErrorDialogOpen(true);
            }

            // Refresh the work orders list
            let fetchUrl = `/api/v1/provider/${provider.id}/work_orders/${business.id}/90`;
            getFromApi(fetchUrl, user, (response) => {
              let workorders = response.result || [];
              setWorkOrders(workorders.sort((a, b) => a.id - b.id));
              toast({
                title: "Upload Successful",
                description: `${response.count || 'Multiple'} work orders imported successfully`,
              });
            }, (error) => {
              console.error("Error fetching work orders:", error);
            });
          } else {
            throw new Error(response.message || "Upload failed");
          }
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        },
        (error) => {
          console.error("Error uploading work orders:", error);
          toast({
            title: "Upload Failed",
            description: error.message || "Could not upload work orders",
            variant: "destructive",
          });
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }

      );
    }
  };

  const createInvoiceBatchFromCompletedWorkOrders = async () => {
    if (!provider || !user) {
      toast({
        title: "Error",
        description: "Provider or user information is missing",
        variant: "destructive",
      });
      return;
    }

    // Get the IDs of all completed work orders that don't have an invoice
    const completedWorkOrders = workOrders.filter(wo =>
      wo.status?.toLowerCase() === 'completed' && !wo.invoiceId
    );

    if (completedWorkOrders.length === 0) {
      toast({
        title: "No Completed Work Orders",
        description: "There are no completed work orders available to create invoices from.",
        variant: "destructive",
      });
      return;
    }

    setIsCreatingBatch(true);

    const url = `/api/v1/invoice/batch/${provider.id}/from_work_orders`
    const workOrderIds = completedWorkOrders.map(wo => wo.id);
    console.log("url:", url)
    console.log("workOrderIds:", workOrderIds)
    postToApi(url, user, workOrderIds,
      (response) => {
        console.log("response:", response.result);
        let batch = response.result;
        fetchWorkOrders();
        setIsCreatingBatch(false);
        toast({
          title: "Batch Creation Successful",
          description: `${batch.Invoices.length || 'Multiple'} invoices created successfully`,
        });
        batch.url = `/api/v1/invoice/batch/${batch.id}/export/xlsx`;
        setBatchResultData(batch);
        setIsInfoDialogOpen(true);
      },
      (error) => {
        console.error("Error creating invoice batch:", error);
        setIsCreatingBatch(false);
        toast({
          title: "Batch Creation Failed",
          description: "Could not create invoice batch from completed work orders",
          variant: "destructive",
        });
      }
    );

  };

  return (
    <div>
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-medium mb-4">Work Orders</h3>
          <div className="flex justify-end items-right mb-2">

            <div className="flex gap-2">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileUpload}
                accept=".xlsx,.xls"
                className="hidden"
                id="excel-upload"
              />
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Upload className="h-4 w-4" />
                {isUploading ? "Uploading..." : "Import ClickUp Export"}
              </Button>

              <Button
                className="flex items-center gap-2"
                onClick={createInvoiceBatchFromCompletedWorkOrders}
                disabled={workOrders.filter(wo => wo.status?.toLowerCase() === 'completed').length === 0}
              >
                {isCreatingBatch ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Creating Batch...
                  </>
                ) : (
                  <>
                    <Receipt className="h-4 w-4" />
                    Create Invoice Batch from Completed Work Orders
                  </>
                )}
              </Button>

            </div>
          </div>
          {workOrders && workOrders.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Team</TableHead>
                    <TableHead>Service Records</TableHead>
                    <TableHead>Invoice</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workOrders.map((workOrder) => (
                    <WorkOrderItem
                      key={workOrder.id}
                      workOrder={workOrder}
                      onSelect={() => setSelectedWorkOrder(workOrder)}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-muted-foreground">No work orders found for this business.</p>
          )}

        </CardContent>
      </Card>


      {/* Error Dialog */}
      <Dialog open={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Upload Results
            </DialogTitle>
          </DialogHeader>

          {errorData && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Successfully Added</p>
                  <p className="text-2xl font-bold text-green-700">{errorData.addedCount}</p>
                </div>
                <div className="bg-red-50 p-3 rounded-md border border-red-200">
                  <p className="text-sm font-medium text-red-800">Errors</p>
                  <p className="text-2xl font-bold text-red-700">{errorData.errorCount}</p>
                </div>
              </div>

              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium mb-2">Error Details</p>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={errorData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    View detailed error report
                  </a>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsErrorDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Result info Dialog */}
      <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Batch Creation Results
            </DialogTitle>
          </DialogHeader>

          {batchResultData && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Created Invoice Batch</p>
                  <p className="text-2xl font-bold text-green-700"># {batchResultData.num}</p>
                </div>

                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{batchResultData.Invoices.length}</p>
                  <p className="text-sm font-medium text-green-800">Invoices Added</p>
                </div>


                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{currencyValue(batchResultData.totalValue)}</p>
                  <p className="text-sm font-medium text-green-800">Total Value</p>
                </div>

              </div>

              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium mb-2">Export Download</p>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <Button
                    variant="link"
                    className="text-sm text-blue-600 hover:underline p-0 h-auto"
                    onClick={() => window.open(batchResultData.url, '_blank')}
                  >
                    Download your invoice batch
                  </Button>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsInfoDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
