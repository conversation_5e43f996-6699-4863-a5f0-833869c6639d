import { Card, CardContent } from "@/components/ui/card";

export function LocationList({ locations, onLocationClick }) {
  return (
    <div className="space-y-4">
      {locations && locations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {locations.map((loc) => (
            <Card
              key={loc.id || loc.name}
              className="cursor-pointer transition-all hover:shadow-md"
              onClick={() => {
                //console.log("select location", loc)
                onLocationClick(loc)
              }}
            >
              <CardContent className="p-4">
                <h3 className="font-medium">{loc.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {loc.address}
                </p>
                <p className="text-sm text-muted-foreground">
                  {loc.city}, {loc.province}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No Locations for this business.</p>
      )}
    </div>
  );
}