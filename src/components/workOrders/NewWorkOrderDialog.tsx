import { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { getFromApi, postToApi } from '@/lib/api';
import { ISessionUser } from '@/types';

interface NewWorkOrderDialogProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  workOrders: any[];
  setWorkOrders: (workOrders: any[]) => void;
}
export function NewWorkOrderDialog({ isOpen, setIsOpen, workOrders, setWorkOrders }: NewWorkOrderDialogProps) {
  const [customers, setCustomers] = useState([]); //The business are the customers
  const [locations, setLocations] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const { user } : { user: ISessionUser } = useContext(ConfigContext);
  const {
    provider,
    setupUser
  } = useContext(BusinessContext);
  const { toast } = useToast();

  const [newWorkOrder, setNewWorkOrder] = useState({
    businessId: "",
    locationId: "",
    description: "",
    price: "",
    startDate: ""
  });

  useEffect(() => {
    // Fetch customers
    if (provider) {
      getFromApi(`/api/v1/provider/${provider.id}/customers`, user, 
        (response) => {
        console.log("customers", response.result)
        setCustomers(response.result || []);
      }, (error) => {
        console.error("Error fetching customers:", error);
      });
    }
  }, [provider, user])

  const handleCustomerChange = (businessId) => { // If Customer change, locations will change
    let locations = customers.find(c => c.id == businessId).locations
    let customer = customers.find(c => c.id == businessId)
    setLocations(locations)
    setSelectedCustomer(customer);
    setNewWorkOrder({ ...newWorkOrder, businessId, locationId: "" });
  };

  const handleCreateWorkOrder = () => {
    console.log("newWorkOrder", newWorkOrder);
    if (
      !newWorkOrder.businessId ||
      !newWorkOrder.locationId ||
      !newWorkOrder.description ||
      !newWorkOrder.price
    ) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (provider && user) {
      const url = `/api/v1/work_order`; //TODO: check if this is correct
      const data = {
        ...newWorkOrder,
        businessId: Number(newWorkOrder.businessId),
        locationId: Number(newWorkOrder.locationId),
        price: parseFloat(newWorkOrder.price),
        startDate: newWorkOrder.startDate.toISOString()
      }
      console.log("url:", url)
      console.log("data:", data)
      postToApi(url, user, data,
        (response) => {
          const createdWorkOrder = response.result;
          console.log("response:", response.result);

          setWorkOrders([...workOrders, createdWorkOrder]);
          setIsOpen(false);
          setNewWorkOrder({
            businessId: "",
            locationId: "",
            description: "",
            price: "",
            startDate: ""
          });
          setSelectedCustomer(null);
          toast({
            title: "Work Order Created",
            description: `Work order #${createdWorkOrder.num} created successfully`,
          });
        },
        (error) => {
          console.error("Error creating work order:", error);
          toast({
            title: "Creation Failed",
            description: "Could not create work order",
            variant: "destructive",
          });
        }
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create New Work Order</DialogTitle>
        </DialogHeader>
        {/* Customer Selection */}
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="customer" className="text-right">
              Customer*
            </Label>
            <div className="col-span-3">
              <Select
                value={newWorkOrder.businessId}
                onValueChange={handleCustomerChange}
              >
                <SelectTrigger id="customer">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id.toString()}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Location Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="location" className="text-right">
              Location*
            </Label>
            <div className="col-span-3">
              <Select
                value={newWorkOrder.locationId}
                onValueChange={(value) => setNewWorkOrder({ ...newWorkOrder, locationId: value })}
                disabled={!selectedCustomer}
              >
                <SelectTrigger id="location">
                  <SelectValue placeholder={selectedCustomer ? "Select location" : "Select a customer first"} />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id.toString()}>
                      {location.address}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Description Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description*
            </Label>
            <div className="col-span-3">
              <Textarea
                id="description"
                placeholder="Enter work order description"
                value={newWorkOrder.description}
                onChange={(e) => setNewWorkOrder({ ...newWorkOrder, description: e.target.value })}
              />
            </div>
          </div>
          {/* Price Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="price" className="text-right">
              Price*
            </Label>
            <div className="col-span-3">
              <Input
                id="price"
                type="number"
                value={newWorkOrder.price}
                onChange={(e) => setNewWorkOrder({ ...newWorkOrder, price: e.target.value })}
              />
            </div>
          </div>
          {/* Start Date Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="date" className="text-right">
              Start Date
            </Label>
            <div className="col-span-3">
              <Input
                id="date"
                type="date"
                value={newWorkOrder.startDate}
                onChange={(e) => setNewWorkOrder({ ...newWorkOrder, startDate: e.target.value })}
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreateWorkOrder}>
            Create Work Order
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}