import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { FileText, Users, ClipboardList, ArrowLeft, Check, Receipt } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { TeamMembersTab } from './tabs/TeamMembersTab';
import { TeamProvider } from "@/context/TeamContext";

import { WorkOrderStatusBadge } from './WorkOrderItem';


export function WorkOrderDetail({ workOrder, onBack, onStatusChange, onCreateInvoice, updateSelectedWorkOrder }) {

  const [activeTab, setActiveTab] = useState("details");

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  const readyToInvoice = (workOrder) => {
    if (workOrder.invoiceId) return false;

    switch (workOrder.status?.toLowerCase()) {
      case 'completed':
      case 'ready to invoice':
        return true;

      default:
        return false;
    }
  };

  let addr = workOrder.location?.address || workOrder.fields?.building_number || 'N/A'
  let description = workOrder.description || workOrder.fields?.description || workOrder.fields?.lastComment || 'No description'

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Work Orders
        </Button>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Work Order #{workOrder.num}</h1>
          <p className="text-muted-foreground">
            {workOrder.business?.name} - {formatDate(workOrder.started)}
          </p>
        </div>
        <div className="flex gap-2">
          {readyToInvoice(workOrder) && (
            <Button
              className="flex items-center gap-2"
              onClick={() => onCreateInvoice(workOrder.id)}
            >
              <Receipt className="h-4 w-4" />
              Create Invoice
            </Button>
          )}
          {workOrder.status !== 'invoiced' && workOrder.status !== 'completed' && (
            <Button
              className="flex items-center gap-2"
              onClick={() => onStatusChange(workOrder, 'completed')}
            >
              <Check className="h-4 w-4" />
              Mark Completed
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <div className="flex justify-between items-center">
                <span>Current Status:</span>
                <WorkOrderStatusBadge status={workOrder.status} />
              </div>

              <div>
                <label className="text-sm font-medium">Change Status:</label>
                <Select
                  defaultValue={workOrder.status}
                  onValueChange={(value) => onStatusChange(workOrder, value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {workOrder.status === 'invoiced' ? (
                      <SelectItem value="invoiced" disabled>Invoiced</SelectItem>
                    ) : (
                      <>
                        <SelectItem value="To Do">To Do</SelectItem>
                        <SelectItem value="Pending">Pending</SelectItem>
                        <SelectItem value="Scheduled">Scheduled</SelectItem>
                        <SelectItem value="In Progress">In Progress</SelectItem>
                        <SelectItem value="Dispatched">Dispatched</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Cancelled">Cancelled</SelectItem>
                        <SelectItem value="Parts on Order">Parts on Order</SelectItem>
                        <SelectItem value="Needs Quote">Needs Quote</SelectItem>
                        <SelectItem value="Pending Client Followup">Pending Client Followup</SelectItem>
                        <SelectItem value="Ready to Close">Ready to Close</SelectItem>
                        <SelectItem value="Ready to Invoice">Ready to Invoice</SelectItem>
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-muted-foreground">Business:</span>
                <p className="font-medium">{workOrder.business?.name || 'N/A'}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Location:</span>
                <p className="font-medium">{addr}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Contact:</span>
                <p className="font-medium">{workOrder.contact?.name || workOrder.location?.contactName || 'N/A'}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Work Order Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-muted-foreground">Started:</span>
                <p className="font-medium">{formatDate(workOrder.started)}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Completed:</span>
                <p className="font-medium">{formatDate(workOrder.completed) || 'Not completed'}</p>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Description:</span>
                <p className="font-medium">{description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="team">Team Members</TabsTrigger>
          <TabsTrigger value="service-records">Service Records</TabsTrigger>
          <TabsTrigger value="photos">Photos</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Work Order Information</h3>
                  <p>{workOrder.description}</p>
                </div>

                {workOrder.invoice && (
                  <div>
                    <h3 className="text-lg font-medium mb-2">Invoice Information</h3>
                    <p>Invoice #{workOrder.invoice.number}</p>
                    <p>Total: ${workOrder.invoice.total?.toFixed(2) || '0.00'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TeamProvider>
          <TeamMembersTab
            workOrder={workOrder}
            updateSelectedWorkOrder={updateSelectedWorkOrder}
          />
        </TeamProvider>

        <TabsContent value="service-records" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              {workOrder.serviceRecords && workOrder.serviceRecords.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Equipment</TableHead>
                      <TableHead>Service Type</TableHead>
                      <TableHead>Technician</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Notes</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {workOrder.serviceRecords.map((record, index) => (
                      <TableRow key={index}>
                        <TableCell>{record.equipment?.name || 'N/A'}</TableCell>
                        <TableCell>{record.serviceType || 'Standard'}</TableCell>
                        <TableCell>{record.technician?.name || 'N/A'}</TableCell>
                        <TableCell>{formatDate(record.date)}</TableCell>
                        <TableCell className="max-w-[200px] truncate">
                          {record.notes || 'No notes'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-6">
                  <ClipboardList className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Service Records</h3>
                  <p className="text-muted-foreground">
                    No service records have been created for this work order.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="photos" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              {workOrder.Assets && workOrder.Assets.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {workOrder.Assets.map((photo, index) => (
                    <div key={index} className="aspect-square rounded-md overflow-hidden">
                      <h5>{photo.name}</h5>
                      <img
                        src={photo.url}
                        alt={`Work order photo ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Photos</h3>
                  <p className="text-muted-foreground">
                    No photos have been uploaded for this work order.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notes" className="space-y-4 mt-4">
          <Card>
            <CardContent className="pt-6">
              {workOrder.notes && workOrder.notes.length > 0 ? (
                <div className="space-y-4">
                  {workOrder.notes.map((note, index) => (
                    <div key={index} className="p-4 border rounded-md">
                      <div className="flex justify-between items-start mb-2">
                        <span className="font-medium">{note.author || 'System'}</span>
                        <span className="text-sm text-muted-foreground">
                          {formatDate(note.date)}
                        </span>
                      </div>
                      <p>{note.content}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Notes</h3>
                  <p className="text-muted-foreground">
                    No notes have been added for this work order.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}