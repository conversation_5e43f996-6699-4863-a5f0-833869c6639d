
import { TableCell, TableRow } from "@/components/ui/table";
import { Users, ClipboardList } from "lucide-react";
import { Badge } from "@/components/ui/badge";

/*
Statuses
      
-Cancelled - If  job is cancelled for any reason AND non-billable, it's moved here so that it's not fully deleted from the system.
-Completed - This is a ClickUp Specific status. We try not to use it because things are invisible once moved to this status. You can open up 'Completed' jobs, but we avoid using this if we can.

*/
export function WorkOrderStatusBadge({ status }) {
	switch (status?.toLowerCase()) {
		case 'to do':
			return <Badge className="bg-gray-500">To Do</Badge>;
		case 'invoiced':
			return <Badge className="bg-green-500">Invoiced</Badge>;
		case 'completed':
			return <Badge className="bg-green-500">Completed</Badge>;
		case 'ready to close':
			return <Badge className="bg-green-500">Ready to Close</Badge>;
		case 'in progress':
			return <Badge className="bg-blue-500">In Progress</Badge>;
		case 'dispatched':
			return <Badge className="bg-blue-500">Dispatched</Badge>;
		case 'scheduled':
			return <Badge className="bg-yellow-500">Scheduled</Badge>;
		case 'needs quote':
			return <Badge className="bg-orange-500">Needs Quote</Badge>;
		case 'parts on order':
			return <Badge className="bg-orange-500">Parts on Order</Badge>;
		case 'pending':
			return <Badge className="bg-orange-500">Pending</Badge>;
		case 'pending client followup':
			return <Badge className="bg-orange-500">Pending Client</Badge>;
		case 'ready to invoice':
			return <Badge className="bg-green-500">Ready to Invoice</Badge>;
		case 'cancelled':
			return <Badge className="bg-red-500">Cancelled</Badge>;
		default:
			return <Badge className="bg-gray-500">Unknown</Badge>;
	}
}

export function WorkOrderItem({ workOrder, onSelect }) {
	const formatDate = (dateString) => {
		if (!dateString) return 'N/A';
		return new Date(dateString).toLocaleDateString();
	};


	let addr = workOrder.location?.address || workOrder.fields?.building_number || 'N/A'
	let description = workOrder.description || workOrder.fields?.description || workOrder.fields?.lastComment || 'No description'

	return (
		<TableRow
			className="cursor-pointer hover:bg-muted/50"
			onClick={onSelect}
		>
			<TableCell className="font-medium">#{workOrder.num}</TableCell>
			<TableCell>{workOrder.business?.name || 'N/A'}</TableCell>
			<TableCell className="max-w-[200px] truncate" title={addr}>
				{addr}
			</TableCell>
			<TableCell>{formatDate(workOrder.started)}</TableCell>
			<TableCell className="max-w-[200px] truncate" title={description}>
				{description || 'No description'}
			</TableCell>
			<TableCell>
				{workOrder.team && workOrder.team.length > 0 ? (
					<div className="flex items-center gap-1">
						<Users className="h-4 w-4 text-muted-foreground" />
						<span>{workOrder.team.length}</span>
					</div>
				) : (
					<span className="text-muted-foreground text-sm">None</span>
				)}
			</TableCell>
			<TableCell>
				{workOrder.serviceRecords && workOrder.serviceRecords.length > 0 ? (
					<div className="flex items-center gap-1">
						<ClipboardList className="h-4 w-4 text-muted-foreground" />
						<span>{workOrder.serviceRecords.length}</span>
					</div>
				) : (
					<span className="text-muted-foreground text-sm">None</span>
				)}
			</TableCell>
			<TableCell>
				{workOrder.invoice ? (
					<span className="font-medium">#{workOrder.invoice.number}</span>
				) : (
					<span className="text-muted-foreground text-sm">Not invoiced</span>
				)}
			</TableCell>
			<TableCell><WorkOrderStatusBadge status={workOrder.status} /></TableCell>
		</TableRow>
	);
}