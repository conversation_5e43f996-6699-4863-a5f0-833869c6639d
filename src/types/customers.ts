import { IRelatedUser, IRelatedLocation } from "./"
export interface ICustomerRes {
	id: number;
	userId: number;
	uid: string;
	uuid: string;
	name: string;
	email: string;
	phone: string;
	address: string;
	city: string;
	province: string;
	country: string;
	zipcode: string;
	latlng: string;
	description: string;
	type: string;
	currency: string;
	locale: string;
	rating: string;
	photo: string;
	banner: string;
	background: string;
	accountName: string;
	accountNumber: string;
	bank: string;
	sortCode: string;
	paymentTerms: string;
	taxId: string;
	nid: string;
	style: string;
	colour: string;
	summary: string;
	session: string;
	signal: string;
	enabled: boolean;
	facebook: string;
	instagram: string;
	twitter: string;
	youtube: string;
	telegram: string;
	whatsapp: string;
	website: string;
	flags: string[] | null;
	User: IRelatedUser;
	locations: IRelatedLocation[] | null;
	Contacts: any | null;
	Contractors: any | null;
	Configs: any | null;
	Categories: any | null;
	Roles: any | null;
	Subscriptions: any | null;
	Customers: any | null;
	CreatedAt: string;
	UpdatedAt: string;
	UpdatedBy: number;
	fields: ICustomField[];
}

export interface ICustomerRelatedRes{
	Configs: any | null;
  Contacts: any | null;
  Contractors: any | null;
  CreatedAt: string;
  Customers: any | null;
  Roles: any | null;
  Subscriptions: any | null;
  UpdatedAt: string;
  UpdatedBy: number;
  User: IRelatedUser;
  accountName: string;
  accountNumber: string;
  address: string;
  background: string;
  bank: string;
  banner: string;
  categories: any | null;
  city: string;
  colour: string;
  country: string;
  currency: string;
  description: string;
  email: string;
  enabled: boolean;
  facebook: string;
  flags: string[];
  id: number;
  instagram: string;
  latlng: string;
  locale: string;
  locations: any | null;
  name: string;
  nid: string;
  paymentTerms: string;
  phone: string;
  photo: string;
  province: string;
  rating: string;
  session: string;
  signal: string;
  sortCode: string;
  style: string;
  summary: string;
  taxId: string;
  telegram: string;
  twitter: string;
  type: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
  youtube: string;
  zipcode: string;
}

export interface ICustomField {
  type: 'text' | 'number' | 'date' | 'select' | 'checkbox' | 'textarea';
  value: any;
  label: string;
  placeholder?: string;
  options?: { label: string; value: any }[];
  required: boolean;
}